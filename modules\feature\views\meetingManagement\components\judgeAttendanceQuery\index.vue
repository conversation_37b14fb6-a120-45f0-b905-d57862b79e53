<!-- 人员参会查询 -->
<template>
    <div class="judge-attendance-container">
        <div class="flex">
            <div class="query-container">
                <PeopleSelector
                    class="selector"
                    placeholder="人员"
                    v-model="accountList"
                    :options="externalOptions"
                ></PeopleSelector>
                <div class="flex">
                    <el-date-picker
                        class="selector date-selector"
                        v-model="week"
                        type="week"
                        placeholder="选择周"
                        ref="dataPicker"
                        :format="dateFormat"
                        :clearable="false"
                        :picker-options="pickerOptions"
                    >
                    </el-date-picker>
                    <el-button
                        class="qucik-access"
                        type="primary"
                        @click="handleQuickAccess('本周')"
                        >本周</el-button
                    >
                    <el-button type="primary" @click="handleQuickAccess('下周')"
                        >下周</el-button
                    >
                </div>
            </div>

            <el-button class="action-button" type="primary" @click="getList"
                >查询</el-button
            >
            <el-button class="action-button" type="primary" @click="handleReset"
                >重置</el-button
            >
        </div>
        <el-table
            class="judge-attendance-table"
            :data="tableList"
            :header-cell-style="{
                'text-align': 'center',
                'border': '1px solid #8c8c8c'
            }"
            :cell-style="{ border: '1px solid #8c8c8c!important' }"
            height="calc(100vh - 170px)"
            empty-text="无评委参会数据"
        >
            <el-table-column prop="userName" label="人员" align="center">
            </el-table-column>
            <el-table-column
                v-for="(i, index) in dateArr"
                :label="i"
                :key="i"
                align="center"
            >
                <el-table-column
                    :key="weekList[index]"
                    :label="weekList[index]"
                    align="center"
                >
                    <el-table-column
                        v-for="j in timePeriodList"
                        :key="j"
                        :label="j"
                        align="center"
                        width="70"
                    >
                        <template slot-scope="scope">
                            <el-popover
                                placement="top-start"
                                width="500"
                                trigger="hover"
                            >
                                <div
                                    slot="reference"
                                    :style="
                                        handleWeekData(
                                            scope.row,
                                            dateArr[index],
                                            j,
                                            'style'
                                        )
                                    "
                                >
                                    <!-- {{
                                        handleWeekData(
                                            scope.row,
                                            dateArr[index],
                                            j,
                                            'numbers'
                                        ) || ''
                                    }} -->
                                </div>
                                <el-table
                                    style="width: 100%"
                                    :header-cell-style="{
                                        'text-align': 'center'
                                    }"
                                    :data="
                                        handleWeekData(
                                            scope.row,
                                            dateArr[index],
                                            j,
                                            'tableList'
                                        )
                                    "
                                >
                                    <el-table-column
                                        prop="startTime"
                                        label="预计开始时间"
                                        min-width="100"
                                        align="center"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                        prop="endTime"
                                        label="预计完成时间"
                                        min-width="100"
                                        align="center"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                        prop="meetingTitle"
                                        label="会议名称"
                                        min-width="200"
                                    >
                                    </el-table-column>
                                </el-table>
                            </el-popover>
                        </template>
                    </el-table-column>
                </el-table-column>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants.js';
import moment from 'moment';
import PeopleSelector from 'Components/PeopleSelector.vue';
import { getDaysOfWeek } from '../../commonFunction';

const weekList = [
    '星期一',
    '星期二',
    '星期三',
    '星期四',
    '星期五',
    '星期六',
    '星期日'
];
const timePeriodList = ['上午', '下午'];
export default {
    name: 'JudgeAttendanceQuery',
    components: { PeopleSelector },
    data() {
        return {
            week: new Date(),
            pickerOptions: {
                firstDayOfWeek: 1
            },
            CONSTANTS,
            tableList: [],
            dateArr: [],
            weekList,
            timePeriodList,
            accountList: []
        };
    },
    computed: {
        dateFormat() {
            const startOfWeek = moment(this.week)
                .startOf('week')
                .format('YYYY/M/D');
            const endOfWeek = moment(this.week)
                .endOf('week')
                .format('YYYY/M/D');
            return `${startOfWeek} - ${endOfWeek}`;
        },
        externalOptions() {
            return this.$store.state.feature.externalStaff;
        }
    },
    mounted() {
        this.dateArr = getDaysOfWeek(this.week, 'YYYY-MM-DD');
        this.getList();
    },
    activated() {
        this.dateArr = getDaysOfWeek(this.week, 'YYYY-MM-DD');
        this.getList();
    },
    methods: {
        /**
         * 重置
         */
        handleReset() {
            this.accountList = [];
            this.handleQuickAccess('本周');
            this.getList();
        },
        /**
         * 获取列表
         */
        async getList() {
            this.tableList = [
                {
                    firstOrgName: '软件开发部',
                    userAccount: 'wangyunpeng',
                    userName: '王云鹏111111',
                    meetingJudgesPartVoList: [
                        {
                            dateVal: '2025-09-15',
                            morningCount: 2,
                            afternoonCount: 1,
                            morningMeetList: [
                                {
                                    startTime: '09:00',
                                    endTime: '10:30',
                                    meetingTitle: '项目评审会议A'
                                },
                                {
                                    startTime: '10:30',
                                    endTime: '12:00',
                                    meetingTitle: '技术讨论会议B'
                                }
                            ],
                            afternoonMeetList: [
                                {
                                    startTime: '14:00',
                                    endTime: '16:00',
                                    meetingTitle: '需求分析会议C'
                                }
                            ]
                        },
                        {
                            dateVal: '2025-09-16',
                            morningCount: 1,
                            afternoonCount: 0,
                            morningMeetList: [
                                {
                                    startTime: '09:30',
                                    endTime: '11:00',
                                    meetingTitle: '周例会'
                                }
                            ],
                            afternoonMeetList: []
                        }
                    ],
                    judgesUnavailableTimeList: [
                        {
                            id: null,
                            account: null,
                            allDayFlag: '全天',
                            startDate: '2025-09-10',
                            endDate: '2025-10-10',
                            startTime: '00:00',
                            endTime: '23:59',
                            reason: ''
                        },
                        {
                            id: null,
                            account: null,
                            allDayFlag: '全天',
                            startDate: '2025-09-06',
                            endDate: '2025-10-08',
                            startTime: '00:00',
                            endTime: '23:59',
                            reason: '1'
                        },
                        {
                            id: null,
                            account: null,
                            allDayFlag: '全天',
                            startDate: '2025-09-10',
                            endDate: '2025-10-06',
                            startTime: '00:00',
                            endTime: '23:59',
                            reason: '1'
                        },
                        {
                            id: null,
                            account: null,
                            allDayFlag: '全天',
                            startDate: '2025-09-10',
                            endDate: '2025-10-06',
                            startTime: '00:00',
                            endTime: '23:59',
                            reason: '2'
                        }
                    ]
                },
                {
                    firstOrgName: '软件开发部',
                    userAccount: 'anqi2',
                    userName: '安琦',
                    meetingJudgesPartVoList: [],
                    judgesUnavailableTimeList: [
                        {
                            id: null,
                            account: null,
                            allDayFlag: null,
                            startDate: '2025-08-21',
                            endDate: '2025-09-19',
                            startTime: '09:00',
                            endTime: '18:00',
                            reason: '请假'
                        }
                    ]
                },
                {
                    firstOrgName: '软件开发部',
                    userAccount: 'yangmiaomiao',
                    userName: '杨苗苗',
                    meetingJudgesPartVoList: [],
                    judgesUnavailableTimeList: [
                        {
                            id: null,
                            account: null,
                            allDayFlag: null,
                            startDate: '2025-09-01',
                            endDate: '2025-09-15',
                            startTime: '09:00',
                            endTime: '18:00',
                            reason: '请假'
                        }
                    ]
                },
                {
                    firstOrgName: '管理部',
                    userAccount: 'gaorui',
                    userName: '高蕊',
                    meetingJudgesPartVoList: [],
                    judgesUnavailableTimeList: [
                        {
                            id: null,
                            account: null,
                            allDayFlag: null,
                            startDate: '2025-04-16',
                            endDate: '2025-09-30',
                            startTime: '09:00',
                            endTime: '18:00',
                            reason: '请假'
                        }
                    ]
                },
                {
                    firstOrgName: '工艺部',
                    userAccount: 'suntongjia',
                    userName: '孙同嘉',
                    meetingJudgesPartVoList: [],
                    judgesUnavailableTimeList: [
                        {
                            id: null,
                            account: null,
                            allDayFlag: null,
                            startDate: '2025-09-01',
                            endDate: '2025-12-31',
                            startTime: '08:00',
                            endTime: '17:00',
                            reason: '请假'
                        }
                    ]
                },
                {
                    firstOrgName: '工艺部',
                    userAccount: 'conghuacheng',
                    userName: '丛华成',
                    meetingJudgesPartVoList: [],
                    judgesUnavailableTimeList: [
                        {
                            id: null,
                            account: null,
                            allDayFlag: null,
                            startDate: '2025-09-15',
                            endDate: '2025-09-15',
                            startTime: '07:00',
                            endTime: '13:00',
                            reason: '请假'
                        }
                    ]
                },
                {
                    firstOrgName: '结构开发部',
                    userAccount: 'houguijia',
                    userName: '候桂佳',
                    meetingJudgesPartVoList: [],
                    judgesUnavailableTimeList: [
                        {
                            id: null,
                            account: null,
                            allDayFlag: null,
                            startDate: '2025-09-11',
                            endDate: '2025-09-28',
                            startTime: '08:00',
                            endTime: '17:00',
                            reason: '请假'
                        }
                    ]
                },
                {
                    firstOrgName: '结构开发部',
                    userAccount: 'shiguokun',
                    userName: '石国坤',
                    meetingJudgesPartVoList: [],
                    judgesUnavailableTimeList: [
                        {
                            id: null,
                            account: null,
                            allDayFlag: null,
                            startDate: '2025-09-13',
                            endDate: '2025-09-30',
                            startTime: '08:00',
                            endTime: '17:00',
                            reason: '请假'
                        }
                    ]
                },
                {
                    firstOrgName: '结构开发部',
                    userAccount: 'liyiqi',
                    userName: '李义奇',
                    meetingJudgesPartVoList: [],
                    judgesUnavailableTimeList: [
                        {
                            id: null,
                            account: null,
                            allDayFlag: null,
                            startDate: '2025-09-17',
                            endDate: '2025-09-19',
                            startTime: '08:00',
                            endTime: '17:00',
                            reason: '请假'
                        }
                    ]
                },
                {
                    firstOrgName: '硬件开发部',
                    userAccount: 'congrize',
                    userName: '丛日泽',
                    meetingJudgesPartVoList: [],
                    judgesUnavailableTimeList: [
                        {
                            id: null,
                            account: null,
                            allDayFlag: null,
                            startDate: '2025-09-18',
                            endDate: '2025-09-18',
                            startTime: '08:00',
                            endTime: '17:00',
                            reason: '请假'
                        }
                    ]
                },
                {
                    firstOrgName: '硬件开发部',
                    userAccount: 'liuming',
                    userName: '刘铭',
                    meetingJudgesPartVoList: [
                        {
                            dateVal: '2025-09-15',
                            morningCount: 1,
                            afternoonCount: 2,
                            morningMeetList: [
                                {
                                    startTime: '10:00',
                                    endTime: '11:30',
                                    meetingTitle: '硬件设计评审'
                                }
                            ],
                            afternoonMeetList: [
                                {
                                    startTime: '13:00',
                                    endTime: '14:30',
                                    meetingTitle: '产品规划会议'
                                },
                                {
                                    startTime: '15:00',
                                    endTime: '17:00',
                                    meetingTitle: '技术交流会'
                                }
                            ]
                        }
                    ],
                    judgesUnavailableTimeList: [
                        {
                            id: null,
                            account: null,
                            allDayFlag: null,
                            startDate: '2025-09-15',
                            endDate: '2025-09-15',
                            startTime: '08:00',
                            endTime: '11:00',
                            reason: '请假'
                        },
                        {
                            id: null,
                            account: null,
                            allDayFlag: null,
                            startDate: '2025-09-15',
                            endDate: '2025-09-18',
                            startTime: '11:00',
                            endTime: '11:00',
                            reason: '请假'
                        }
                    ]
                }
            ];
            return;
            const api = this.$service.feature.meeting.getJudgeAttendanceInfo;
            const startDate = moment(this.week)
                .startOf('week')
                .format('YYYY-MM-DD');
            const endDate = moment(this.week)
                .endOf('week')
                .format('YYYY-MM-DD');
            const params = {
                currentPage: this.page,
                endDate,
                accountList: this.accountList,
                pageSize: this.limit,
                startDate
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.dateArr = getDaysOfWeek(this.week, 'YYYY-MM-DD');
                    this.tableList = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 点击快捷按钮的处理
         * @param {String} type 按钮类型
         */
        handleQuickAccess(type) {
            const currentDate = new Date();
            if (type === '本周') {
                this.week = currentDate;
            } else if (type === '下周') {
                const nextWeekDate = new Date();
                nextWeekDate.setDate(currentDate.getDate() + 7);
                this.week = nextWeekDate;
            }
        },
        /**
         * 获取当前日期的数据
         * @param {Array} rowData 当前行数据
         * @param {String} curDay 日期
         * @param {String} period 上午/下午
         * @param {String} type 类型 'numbers'|'tableList'|'style'
         * @returns {Array|String|Object} 数据
         */
        handleWeekData(rowData, curDay, period, type) {
            const {
                // 评委登记的不可用时间段
                judgesUnavailableTimeList = [],
                // 已经有的会议占用的时间段
                meetingJudgesPartVoList = []
            } = rowData;

            // 检查是否有不可用时间
            const validUnavailableTimeList =
                this.isCurrnetUnavailableTimeInPeriod(
                    judgesUnavailableTimeList,
                    curDay,
                    period
                );

            // 检查是否有会议
            const curMeetingData = meetingJudgesPartVoList.filter(
                (i) => i.dateVal === curDay
            );

            const hasMeeting = curMeetingData.length > 0;
            const hasUnavailableTime = validUnavailableTimeList.length > 0;

            // 获取会议数量
            let meetingCount = 0;
            let meetingList = [];
            if (hasMeeting) {
                meetingCount =
                    period === '上午'
                        ? curMeetingData[0].morningCount
                        : curMeetingData[0].afternoonCount;
                meetingList =
                    period === '上午'
                        ? curMeetingData[0].morningMeetList
                        : curMeetingData[0].afternoonMeetList;
            }

            // 根据type返回不同内容
            if (type === 'style') {
                // 返回样式对象
                if (hasUnavailableTime && hasMeeting && meetingCount > 0) {
                    // 不可用时间和会议重合：红色背景
                    return {
                        backgroundColor: '#ff4d4f',
                        color: '#fff',
                        padding: '4px'
                    };
                } else if (hasMeeting && meetingCount > 0) {
                    // 只有会议：绿色背景
                    return {
                        backgroundColor: '#52c41a',
                        color: '#fff',
                        padding: '4px'
                    };
                } else if (hasUnavailableTime) {
                    // 只有不可用时间：橙色背景
                    return {
                        backgroundColor: '#fa8c16',
                        color: '#fff',
                        padding: '4px'
                    };
                }
                return {};
            } else if (type === 'numbers') {
                // 返回显示的数字
                if (hasUnavailableTime && hasMeeting && meetingCount > 0) {
                    // 不可用时间和会议重合：显示会议数量
                    return meetingCount;
                } else if (hasMeeting && meetingCount > 0) {
                    // 只有会议：显示会议数量
                    return meetingCount;
                } else if (hasUnavailableTime) {
                    // 只有不可用时间：不显示内容
                    return '';
                }
                return '';
            } else if (type === 'tableList') {
                // 返回表格数据
                if (hasUnavailableTime && hasMeeting) {
                    // 合并不可用时间和会议数据
                    return [...validUnavailableTimeList, ...meetingList];
                } else if (hasMeeting) {
                    return meetingList;
                } else if (hasUnavailableTime) {
                    return validUnavailableTimeList;
                }
                return [];
            }

            return null;
        },
        /**
         * 判断不可用时间列表中是否存在与指定日期和时间段重合的项目
         * @param {Array} unavailableTimeList 不可用时间列表
         * @param {String} curDay 当前日期 格式：2025-01-10
         * @param {String} period 时间段 "上午"或"下午"
         * @param {String} type 返回类型 "numbers"返回空，其他返回匹配的项目列表
         * @returns {null|Array} type为"numbers"时返回null，否则返回重合的不可用时间项目
         */
        isCurrnetUnavailableTimeInPeriod(
            unavailableTimeList,
            curDay,
            period,
            type
        ) {
            if (!unavailableTimeList || unavailableTimeList.length === 0) {
                return type === 'numbers' ? null : [];
            }

            // 定义目标时间段
            let targetStartTime;
            let targetEndTime;
            if (period === '上午') {
                targetStartTime = '08:00';
                targetEndTime = '12:00';
            } else if (period === '下午') {
                targetStartTime = '12:00';
                targetEndTime = '18:00';
            } else {
                return type === 'numbers' ? null : [];
            }

            // 创建目标时间段的moment对象
            const targetStart = moment(`${curDay} ${targetStartTime}`);
            const targetEnd = moment(`${curDay} ${targetEndTime}`);

            // 筛选出与目标时间段重合的不可用时间项
            const matchingItems = unavailableTimeList.filter((item) => {
                // 创建不可用时间段的moment对象
                const unavailableStart = moment(
                    `${item.startDate} ${item.startTime}`
                );
                const unavailableEnd = moment(
                    `${item.endDate} ${item.endTime}`
                );

                // 判断时间段是否重合
                // 两个时间段重合的条件：开始时间小于对方的结束时间，且结束时间大于对方的开始时间
                return (
                    unavailableStart.isBefore(targetEnd) &&
                    unavailableEnd.isAfter(targetStart)
                );
            });

            // 根据type返回不同的结果
            if (type === 'numbers') {
                return null;
            }

            return matchingItems;
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.judge-attendance-container {
    height: calc(100vh - 65px);
}

.query-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    width: calc(100% - 140px);
}

.selector {
    width: 220px;
}

.date-selector {
    height: 28px;
}

@media (max-width: 568px) {
    .selector {
        flex: 1 1 100%;
    }
}
// 修改placeholder颜色
::v-deep .selector .el-input__inner::placeholder {
    color: rgba(0, 0, 0, 0.685);
}
.qucik-access {
    margin-left: 10px;
}
.action-button {
    height: 28px;
    margin-left: 15px;
}

.judge-attendance-table {
    margin-top: 10px;
    border: 1px solid #8c8c8c !important;
}

.pagination {
    padding: 5px 0;
    margin: 10px 0 0 0;
}
// 调整table表头颜色
::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border-bottom: 1px solid #dfe6ec !important;
}
</style>
